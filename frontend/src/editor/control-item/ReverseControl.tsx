import React from "react";
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Tooltip,
} from "@mui/material";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../../store";
import { VideoEditorElement, AudioEditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";

interface ReverseControlProps {
  element: VideoEditorElement | AudioEditorElement;
}

const ReverseControl = observer(({ element }: ReverseControlProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  if (!element || !element.properties) {
    return null;
  }

  // 只有视频和音频元素支持倒放
  if (element.type !== "video" && element.type !== "audio") {
    return null;
  }

  const isReversed = (element as any).isReversed || false;

  const handleReverseChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newReversed = event.target.checked;
    store.setElementReversed(element.id, newReversed);
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
        {element.type === "video" ? "视频倒放" : "音频倒放"}
      </Typography>
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <Tooltip
          title={
            isReversed
              ? `关闭${element.type === "video" ? "视频" : "音频"}倒放`
              : `开启${element.type === "video" ? "视频" : "音频"}倒放`
          }
        >
          <FormControlLabel
            control={
              <Switch
                checked={isReversed}
                onChange={handleReverseChange}
                size="small"
                color="primary"
              />
            }
            label={isReversed ? "倒放开启" : "倒放关闭"}
            sx={{
              margin: 0,
              "& .MuiFormControlLabel-label": {
                fontSize: "0.875rem",
                color: isReversed ? "primary.main" : "text.secondary",
              },
            }}
          />
        </Tooltip>
      </Box>
      {isReversed && (
        <Typography
          variant="caption"
          sx={{
            display: "block",
            mt: 1,
            color: "warning.main",
            fontStyle: "italic",
          }}
        >
          注意：倒放功能会影响播放性能，建议在最终导出前开启
        </Typography>
      )}
    </Box>
  );
});

export default ReverseControl;
