import anime from "animejs";
import { makeAutoObservable } from "mobx";
import { Store } from "./Store";
import { isHtmlVideoElement, isHtmlAudioElement } from "../utils";
import { CONSTANTS, ELEMENT_TYPES } from "./constants";
import { audioManager } from "./AudioManager";
import type { EditorElement } from "../types";

// 使用统一的常量定义
const TIMELINE_CONSTANTS = CONSTANTS.TIMELINE;
const MEDIA_TYPES = {
  VIDEO: ELEMENT_TYPES.VIDEO,
  AUDIO: ELEMENT_TYPES.AUDIO,
  GIF: ELEMENT_TYPES.GIF,
} as const;

export class TimelineManager {
  private store: Store;

  // 时间线状态属性
  maxTime: number;
  timelineDisplayDuration: number;
  maxDuration: number;
  currentKeyFrame: number;
  fps: number;
  playing: boolean;
  animationTimeLine: anime.AnimeTimelineInstance;

  // 时间线平移状态
  timelinePan: {
    enabled: boolean;
    isDragging: boolean;
    startX: number;
    offsetX: number;
  };

  // 播放相关
  private startedTime: number = 0;
  private startedTimePlay: number = 0;
  private animationFrameId: number | undefined;
  private reverseVideoCache: Map<string, number> = new Map(); // 缓存倒放视频的最后设置时间

  // 滚轮节流相关
  private _lastWheelTime: number = 0;
  private _wheelAnimationFrame: number | null = null;
  private _pendingDeltaX: number = 0;
  private readonly _wheelThrottleInterval: number =
    TIMELINE_CONSTANTS.WHEEL_THROTTLE_INTERVAL;

  constructor(store: Store) {
    this.store = store;
    this.initializeTimeline();
    this.setupObservables();
  }

  private setupObservables() {
    makeAutoObservable(this, {
      animationTimeLine: false,
    });
  }

  private initializeTimeline() {
    // 初始化时间线状态
    this.maxTime = TIMELINE_CONSTANTS.MAX_TIME;
    this.timelineDisplayDuration = this.maxTime;
    this.maxDuration = TIMELINE_CONSTANTS.DEFAULT_DURATION;
    // 确保时间轴指示器位置始终从0开始，不保存之前的位置
    this.currentKeyFrame = 0;
    this.fps = TIMELINE_CONSTANTS.DEFAULT_FPS;
    this.playing = false;

    // 初始化动画时间线
    this.animationTimeLine = anime.timeline();

    // 初始化时间线平移状态
    this.timelinePan = {
      enabled: true,
      isDragging: false,
      startX: 0,
      offsetX: 0,
    };

    // 设置默认显示时长
    this.setTimelineDisplayDuration(
      TIMELINE_CONSTANTS.DEFAULT_DISPLAY_DURATION
    );
    this.updateMaxTime();
  }

  // ==================== 时间相关方法 ====================

  get currentTimeInMs() {
    return (this.currentKeyFrame * 1000) / this.fps;
  }

  setCurrentTimeInMs(time: number) {
    this.currentKeyFrame = Math.floor((time / 1000) * this.fps);
  }

  /**
   * 重置时间轴指示器位置到0
   * 用于页面刷新或项目加载后确保indicator从头开始
   */
  resetTimelineIndicator() {
    this.currentKeyFrame = 0;
    this.updateTimeTo(0);
  }

  setMaxTime(maxTime: number) {
    this.maxTime = maxTime;
  }

  updateMaxTime() {
    // 保存当前时间，用于后续检查
    const currentTime = this.currentTimeInMs;

    // 获取所有元素的最大endtime
    const maxElementEndTime = this.store.editorElements.reduce(
      (max, element) => {
        return Math.max(max, element.timeFrame.end);
      },
      0
    );

    // 获取所有字幕的最大endtime（转换为毫秒）
    // 检查captionManager是否已经初始化
    let maxCaptionEndTime = 0;
    if (this.store.captionManager && this.store.captionManager.captions) {
      maxCaptionEndTime = this.store.captionManager.captions.reduce(
        (max, caption) => {
          const captionEndTimeMs =
            this.store.captionManager.timeStringToMilliseconds(caption.endTime);
          return Math.max(max, captionEndTimeMs);
        },
        0
      );
    }

    // 取元素和字幕中的最大endtime
    const maxEndTime = Math.max(maxElementEndTime, maxCaptionEndTime);

    // Set a minimum max time of 30 seconds if no elements or captions exist
    this.maxDuration = maxEndTime > 0 ? maxEndTime : 30 * 1000;

    // 如果当前时间超过了新的最大endtime，则调整indicator位置到最大endtime
    if (this.maxDuration > 0 && currentTime > this.maxDuration) {
      this.handleSeek(this.maxDuration);
    }
  }

  // ==================== 播放控制方法 ====================

  setPlaying(playing: boolean) {
    // 检查是否有可播放的内容（元素或字幕）
    const hasElements = this.store.editorElements.length > 0;
    const hasCaptions =
      this.store.captionManager &&
      this.store.captionManager.captions.length > 0;

    // 如果没有任何可播放的内容，则不允许开始播放
    if (playing && !hasElements && !hasCaptions) {
      console.log("没有元素或字幕，无法开始播放");
      return;
    }

    this.playing = playing;

    // 进入播放状态时，取消所有元素的选中状态并隐藏控制框
    if (playing && this.store.canvas) {
      // 取消canvas中所有对象的选中状态
      this.store.canvas.discardActiveObject();
      // 清除store中的选中元素
      this.store.setSelectedElement(null);
      // 取消所有字幕的选中状态
      this.store.deselectAllCaptions();
      // 重新渲染canvas以确保控制框被隐藏
      this.store.canvas.requestRenderAll();
    }

    // 在开始播放前更新所有媒体元素的当前时间
    this.updateMediaElements();

    // 控制所有GIF元素的播放状态
    this._controlAllGifElements(playing);

    if (playing) {
      this.startedTime = Date.now();
      this.startedTimePlay = this.currentTimeInMs;
      this.animationFrameId = requestAnimationFrame(() => {
        this.playFrames();
      });
    } else {
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = undefined;
      }
    }
  }

  private playFrames = () => {
    if (!this.playing) return;

    // 使用 performance.now() 代替 Date.now() 以获得更高精度的时间戳
    const newTime = Date.now() - this.startedTime + this.startedTimePlay;

    // 如果超过最大时间或超过所有元素的最大endtime，停止播放
    if (
      newTime >= this.maxTime ||
      (this.maxDuration > 0 && newTime >= this.maxDuration)
    ) {
      this.currentKeyFrame = 0;
      this.setPlaying(false);
      // 如果超过最大时间，重置到开始；如果超过最大endtime，停留在最大endtime
      const targetTime = newTime >= this.maxTime ? 0 : this.maxDuration;
      this.updateTimeTo(targetTime);
      return;
    }

    // 使用 requestAnimationFrame 而不是 fabric.util.requestAnimFrame
    // 因为原生 API 性能更好
    this.animationFrameId = requestAnimationFrame(this.playFrames);

    // 批量更新以减少重绘
    this.updateTimeTo(newTime);
  };

  /**
   * 更新时间线到指定时间点
   *
   * 该方法负责：
   * 1. 设置当前时间
   * 2. 更新时间线位置
   * 3. 处理元素的可见性
   * 4. 控制媒体元素的播放状态
   * 5. 更新字幕显示
   *
   * @param newTime 新的时间点（毫秒）
   */
  updateTimeTo(newTime: number) {
    // 更新当前时间
    this.setCurrentTimeInMs(newTime);

    // 更新动画时间线位置
    this.animationTimeLine.seek(newTime);

    // 优化：使用一次循环处理所有元素的可见性和媒体播放
    for (const element of this.store.editorElements) {
      // 检查元素是否在当前时间点可见
      const isVisible =
        element.timeFrame.start <= newTime && newTime <= element.timeFrame.end;

      // 更新元素可见性
      if (element.fabricObject) {
        element.fabricObject.visible = isVisible;
      }

      // 处理媒体元素（视频和音频）
      if (element.type === "video" || element.type === "audio") {
        const media = document.getElementById(
          element.properties.elementId
        ) as HTMLMediaElement;

        if (media) {
          // 获取媒体元素的自定义属性
          const mediaStartTime =
            (element.properties as any).mediaStartTime || 0;
          const originalPlaybackSpeed = (element as any).playbackSpeed || 1;
          const isReversed = (element as any).isReversed || false;

          // 计算正确的媒体时间点，考虑播放速度、偏移和倒放
          let mediaTime: number;
          if (isReversed) {
            // 倒放逻辑：从元素结束时间开始倒推
            const elementDuration =
              (element.timeFrame.end - element.timeFrame.start) / 1000;
            const progressInElement =
              (newTime - element.timeFrame.start) / 1000;
            const reversedProgress = elementDuration - progressInElement;
            mediaTime =
              reversedProgress * originalPlaybackSpeed + mediaStartTime;
          } else {
            // 正常播放逻辑
            mediaTime =
              ((newTime - element.timeFrame.start) / 1000) *
                originalPlaybackSpeed +
              mediaStartTime;
          }

          // 根据元素可见性和播放状态控制媒体播放
          if (isVisible) {
            // 设置播放速度
            media.playbackRate = originalPlaybackSpeed;

            // 优化：减少频繁的currentTime设置
            const currentMediaTime = media.currentTime;
            const expectedTime = Math.max(0, mediaTime);
            const timeDiff = Math.abs(currentMediaTime - expectedTime);

            // 对于倒放视频，使用更大的阈值来减少卡顿
            const threshold =
              isReversed && element.type === "video" ? 0.05 : 0.1;

            if (this.playing) {
              // 对于倒放的视频，我们不让它自动播放，而是手动控制时间
              if (element.type === "video" && isReversed) {
                // 倒放视频：暂停视频，只通过currentTime控制帧
                if (!media.paused) {
                  media.pause();
                }

                // 节流倒放视频更新，减少卡顿
                const now = performance.now();
                const elementCacheKey = element.id;
                const lastUpdateTime =
                  this.reverseVideoCache.get(elementCacheKey) || 0;

                const shouldUpdate =
                  timeDiff > threshold && now - lastUpdateTime > 33; // 限制为30fps

                if (shouldUpdate) {
                  media.currentTime = expectedTime;
                  this.reverseVideoCache.set(elementCacheKey, now);

                  // 预加载倒放视频帧
                  this.preloadReverseVideoFrames(element);
                }
              } else {
                // 正常播放或音频元素
                if (timeDiff > 0.1) {
                  media.currentTime = expectedTime;
                }

                if (media.paused) {
                  if (element.type === "audio") {
                    // 使用AudioManager播放，传递播放速度参数
                    audioManager
                      .safePlayAudio(
                        media as HTMLAudioElement,
                        mediaTime,
                        originalPlaybackSpeed
                      )
                      .catch((err) =>
                        console.error(`Error playing audio:`, err)
                      );
                  } else {
                    // 正常视频播放
                    media
                      .play()
                      .catch((err) =>
                        console.error(`Error playing ${element.type}:`, err)
                      );
                  }
                }
              }
            } else if (!this.playing && !media.paused) {
              // 如果不在播放状态但媒体在播放，暂停它
              if (element.type === "audio") {
                audioManager.safeStopAudio(media as HTMLAudioElement, false);
              } else {
                media.pause();
              }
            } else if (!this.playing && media.paused) {
              // 如果不在播放状态且媒体已暂停，设置正确的时间用于预览
              // 只有当时间差异较大时才更新（避免频繁更新导致卡顿）
              if (timeDiff > threshold) {
                media.currentTime = expectedTime;

                // 如果是视频，请求重绘以显示正确帧
                if (element.type === "video") {
                  this.store.canvas?.requestRenderAll();
                }
              }
            }
          } else {
            // 元素不可见，停止播放
            if (element.type === "audio") {
              audioManager.safeStopAudio(media as HTMLAudioElement, true);
            } else {
              media.pause();
            }
          }
        }
      } else if (element.type === "gif") {
        // GIF元素的播放控制和帧同步
        const gifObject = element.fabricObject as any;
        if (
          gifObject &&
          typeof gifObject.play === "function" &&
          typeof gifObject.pause === "function"
        ) {
          if (isVisible) {
            // 如果正在播放，使用正常的播放控制
            if (this.playing && gifObject.getStatus() !== "Playing") {
              gifObject.play();
            } else if (!this.playing) {
              // 如果不在播放状态（如拖动时间轴），根据时间轴位置设置帧
              if (typeof gifObject.setFrameByTime === "function") {
                const needsRedraw = gifObject.setFrameByTime(
                  newTime,
                  element.timeFrame.start
                );
                if (needsRedraw) {
                  // 如果帧发生变化，请求重绘
                  this.store.canvas?.requestRenderAll();
                }
              }
              // 确保GIF处于暂停状态
              if (gifObject.getStatus() === "Playing") {
                gifObject.pause();
              }
            }
          } else if (gifObject.getStatus() === "Playing") {
            // 如果元素不可见，暂停播放
            gifObject.pause();
          }
        }
      }
    }

    // 更新字幕显示
    if (this.store.captionManager) {
      this.store.captionManager.updateCurrentCaption(newTime);
    }

    // 请求重新渲染画布
    this.store.canvas?.requestRenderAll();
  }

  /**
   * 处理时间线跳转
   * 该方法负责在用户在时间线上点击或拖动指示器时更新视图
   *
   * @param seek 跳转的目标时间（毫秒）
   */
  handleSeek(seek: number) {
    if (this.playing) {
      this.setPlaying(false);
    }

    // 更新时间线位置和元素可见性
    this.updateTimeTo(seek);

    // 只在必要时刷新动画（例如，当有动画元素时）
    if (this.store.animations.length > 0) {
      this.store.refreshAnimations();
    }
    this.updateMediaElements();
  }

  // ==================== 媒体元素更新方法 ====================

  /**
   * 更新所有媒体元素的状态
   * 该方法确保所有媒体元素（视频、音频和GIF）的播放状态和当前时间与时间线同步
   */
  updateMediaElements() {
    const mediaElements = this.store.editorElements.filter(
      (element) =>
        element.type === MEDIA_TYPES.VIDEO ||
        element.type === MEDIA_TYPES.AUDIO ||
        element.type === MEDIA_TYPES.GIF
    );

    for (const element of mediaElements) {
      this._updateSingleMediaElement(element);
    }
  }

  private _updateSingleMediaElement(element: any) {
    // 确保元素是媒体类型且有elementId属性
    if (
      (element.type !== MEDIA_TYPES.VIDEO &&
        element.type !== MEDIA_TYPES.AUDIO &&
        element.type !== MEDIA_TYPES.GIF) ||
      !("elementId" in element.properties)
    ) {
      return;
    }

    // GIF元素的特殊处理
    if (element.type === MEDIA_TYPES.GIF) {
      this._updateGifElement(element);
      return;
    }

    const media = document.getElementById(
      element.properties.elementId as string
    );
    if (!isHtmlVideoElement(media) && !isHtmlAudioElement(media)) return;

    const mediaConfig = this._getMediaElementConfig(element);
    const isWithinTimeFrame = this._isElementWithinTimeFrame(element);

    // 设置媒体元素的当前时间和播放速度
    media.currentTime = mediaConfig.mediaTime;
    media.playbackRate = mediaConfig.playbackSpeed;

    // 控制媒体播放状态
    this._controlMediaPlayback(media, element, isWithinTimeFrame);
  }

  private _getMediaElementConfig(element: any) {
    const mediaStartTime = (element.properties as any).mediaStartTime || 0;
    const originalPlaybackSpeed = (element as any).playbackSpeed || 1;

    const mediaTime = Math.max(
      0,
      ((this.currentTimeInMs - element.timeFrame.start) / 1000) *
        originalPlaybackSpeed +
        mediaStartTime
    );

    return {
      mediaTime,
      playbackSpeed: originalPlaybackSpeed,
      mediaStartTime,
    };
  }

  private _isElementWithinTimeFrame(element: any): boolean {
    return (
      this.currentTimeInMs >= element.timeFrame.start &&
      this.currentTimeInMs <= element.timeFrame.end
    );
  }

  private _controlAllGifElements(playing: boolean) {
    const gifElements = this.store.editorElements.filter(
      (element) => element.type === MEDIA_TYPES.GIF
    );

    for (const element of gifElements) {
      const gifObject = element.fabricObject as any;
      if (
        gifObject &&
        typeof gifObject.play === "function" &&
        typeof gifObject.pause === "function"
      ) {
        const isWithinTimeFrame = this._isElementWithinTimeFrame(element);

        if (
          playing &&
          isWithinTimeFrame &&
          gifObject.getStatus() !== "Playing"
        ) {
          gifObject.play();
        } else if (!playing && gifObject.getStatus() === "Playing") {
          gifObject.pause();
        }
      }
    }
  }

  private _updateGifElement(element: any) {
    const gifObject = element.fabricObject as any;
    if (
      gifObject &&
      typeof gifObject.play === "function" &&
      typeof gifObject.pause === "function"
    ) {
      const isWithinTimeFrame = this._isElementWithinTimeFrame(element);

      if (isWithinTimeFrame) {
        // 如果正在播放，正常播放GIF
        if (this.playing && gifObject.getStatus() !== "Playing") {
          gifObject.play();
        } else if (!this.playing) {
          // 如果不在播放状态，根据时间轴位置设置正确的帧
          if (typeof gifObject.setFrameByTime === "function") {
            const needsRedraw = gifObject.setFrameByTime(
              this.currentTimeInMs,
              element.timeFrame.start
            );
            if (needsRedraw) {
              this.store.canvas?.requestRenderAll();
            }
          }
          // 确保GIF处于暂停状态
          if (gifObject.getStatus() === "Playing") {
            gifObject.pause();
          }
        }
      } else if (gifObject.getStatus() === "Playing") {
        // 如果元素不在时间范围内，暂停播放
        gifObject.pause();
      }
    }
  }

  private _controlMediaPlayback(
    media: HTMLMediaElement,
    element: any,
    isWithinTimeFrame: boolean
  ) {
    if (isWithinTimeFrame && this.playing && media.paused) {
      media.play().catch((e) => console.error(`播放${element.type}时出错:`, e));
    } else if ((!isWithinTimeFrame || !this.playing) && !media.paused) {
      media.pause();
    }
  }

  /**
   * 轻量版的媒体元素更新，只更新当前拖拽的元素
   * 用于拖拽过程中提高性能
   * @param currentElementId 当前正在拖拽的元素ID
   */
  updateMediaElementsLite(currentElementId: string) {
    // 只处理当前拖拽的元素，如果是视频、音频或GIF
    const element = this.store.editorElements.find(
      (el) => el.id === currentElementId
    );
    if (
      !element ||
      (element.type !== "video" &&
        element.type !== "audio" &&
        element.type !== "gif")
    )
      return;

    // GIF元素不需要特殊的媒体控制
    if (element.type === "gif") {
      return;
    }

    const media = document.getElementById(element.properties.elementId);
    if (isHtmlVideoElement(media) || isHtmlAudioElement(media)) {
      // 检查是否有自定义的媒体开始时间（用于分割后的元素）
      const mediaStartTime = (element.properties as any).mediaStartTime || 0;
      const originalPlaybackSpeed = (element as any).playbackSpeed || 1;
      const isReversed = (element as any).isReversed || false;

      // 计算媒体当前时间，考虑倒放
      let mediaTime: number;
      if (isReversed) {
        // 倒放逻辑：从元素结束时间开始倒推
        const elementDuration =
          (element.timeFrame.end - element.timeFrame.start) / 1000;
        const progressInElement =
          (this.currentTimeInMs - element.timeFrame.start) / 1000;
        const reversedProgress = elementDuration - progressInElement;
        mediaTime = reversedProgress * originalPlaybackSpeed + mediaStartTime;
      } else {
        // 正常播放逻辑
        mediaTime =
          (this.currentTimeInMs - element.timeFrame.start) / 1000 +
          mediaStartTime;
      }

      // 优化：对于倒放视频，减少频繁的currentTime设置
      const expectedTime = Math.max(0, mediaTime);
      const currentMediaTime = media.currentTime;
      const timeDiff = Math.abs(currentMediaTime - expectedTime);

      // 对于倒放视频使用更大的阈值
      const threshold = isReversed && element.type === "video" ? 0.05 : 0.02;

      if (timeDiff > threshold) {
        media.currentTime = expectedTime;
      }
    }
  }

  // ==================== 时间线平移方法 ====================

  startTimelinePan(startX: number) {
    this.timelinePan.isDragging = true;
    this.timelinePan.startX = startX;
  }

  updateTimelinePan(currentX: number) {
    console.log("updateTimelinePan", this.timelinePan.offsetX);
    if (!this.timelinePan.isDragging) return;

    const deltaX = currentX - this.timelinePan.startX;
    const panFactor = this._calculatePanFactor();

    // Update the offset (negative deltaX because we want to move the timeline in the opposite direction)
    this.timelinePan.offsetX += -deltaX * panFactor;
    this.timelinePan.offsetX = this._getClampedTimelinePanOffset(
      this.timelinePan.offsetX
    );

    this.timelinePan.startX = currentX;
  }

  private _calculatePanFactor(): number {
    return (
      TIMELINE_CONSTANTS.PAN.BASE_FACTOR *
      (this.timelineDisplayDuration /
        TIMELINE_CONSTANTS.PAN.BASE_TIMELINE_LENGTH)
    );
  }

  endTimelinePan() {
    this.timelinePan.isDragging = false;
  }

  resetTimelinePan() {
    this.timelinePan.offsetX = 0;
  }

  private _getClampedTimelinePanOffset(offsetX: number): number {
    const maxAllowedTime = Math.max(this.timelineDisplayDuration, this.maxTime);
    const maxOffset = Math.max(
      this.timelineDisplayDuration * 0.5,
      maxAllowedTime - this.timelineDisplayDuration
    );
    return Math.max(0, Math.min(maxOffset, offsetX));
  }

  // Set timeline pan offset directly (for scrollbar)
  setTimelinePanOffset(offsetX: number) {
    this.timelinePan.offsetX = this._getClampedTimelinePanOffset(offsetX);
  }

  // ==================== 滚轮处理方法 ====================

  // Handle wheel scrolling for timeline with improved performance
  handleTimelineWheel(deltaX: number) {
    // 累积滚动量
    this._pendingDeltaX += deltaX;

    // 如果已经有一个动画帧在等待，不再请求新的
    if (this._wheelAnimationFrame !== null) return;

    // 检查是否需要节流
    const now = performance.now();
    const timeSinceLastWheel = now - this._lastWheelTime;

    if (timeSinceLastWheel < this._wheelThrottleInterval) {
      // 需要节流，使用requestAnimationFrame
      this._wheelAnimationFrame = requestAnimationFrame(() =>
        this._processWheelScroll()
      );
      return;
    }

    // 不需要节流，直接处理
    this._processWheelScroll();
  }

  // 实际处理滚动的内部方法
  private _processWheelScroll() {
    this._wheelAnimationFrame = null;
    this._lastWheelTime = performance.now();

    if (this._pendingDeltaX === 0) return;

    const scrollFactor = this._calculateScrollFactor();
    this.timelinePan.offsetX += this._pendingDeltaX * scrollFactor;
    this.timelinePan.offsetX = this._getClampedTimelinePanOffset(
      this.timelinePan.offsetX
    );

    this._pendingDeltaX = 0;
  }

  private _calculateScrollFactor(): number {
    return (
      TIMELINE_CONSTANTS.PAN.BASE_SCROLL_FACTOR *
      (this.timelineDisplayDuration /
        TIMELINE_CONSTANTS.PAN.BASE_TIMELINE_LENGTH)
    );
  }

  // ==================== 时间线显示方法 ====================

  setTimelineDisplayDuration(duration: number) {
    this.timelineDisplayDuration = duration;
  }

  // Fit the timeline to show all content
  fitTimelineToContent() {
    // Find the maximum end time across all elements
    const maxEndTime = this.store.editorElements.reduce((max, element) => {
      return Math.max(max, element.timeFrame.end);
    }, 0);

    // If there are no elements or they're all at time 0, use a reasonable default (10 seconds)
    if (maxEndTime <= 0) {
      this.setTimelineDisplayDuration(10000);
      this.resetTimelinePan();
      return;
    }

    // 智能计算边距，确保时长是合理的整数秒
    let newDuration: number;

    // 将最大结束时间转换为秒
    const maxEndTimeInSeconds = Math.ceil(maxEndTime / 1000);

    // 根据时长选择合适的显示范围
    if (maxEndTimeInSeconds <= 5) {
      // 对于5秒以内的内容，显示10秒
      newDuration = 10000;
    } else if (maxEndTimeInSeconds <= 10) {
      // 对于5-10秒的内容，显示15秒
      newDuration = 15000;
    } else if (maxEndTimeInSeconds <= 30) {
      // 对于10-30秒的内容，添加20%边距并向上取整到5秒的倍数
      const withMargin = maxEndTimeInSeconds * 1.2;
      newDuration = Math.ceil(withMargin / 5) * 5 * 1000;
    } else {
      // 对于更长的内容，添加10%边距并向上取整到10秒的倍数
      const withMargin = maxEndTimeInSeconds * 1.1;
      newDuration = Math.ceil(withMargin / 10) * 10 * 1000;
    }

    // Update the timelineDisplayDuration
    this.setTimelineDisplayDuration(Math.min(newDuration, this.maxTime));

    // Reset the pan offset to view from the beginning
    this.resetTimelinePan();
  }

  // ==================== 倒放优化方法 ====================

  /**
   * 预加载倒放视频的关键帧以提升性能
   * @param element 视频元素
   */
  private preloadReverseVideoFrames(element: EditorElement) {
    if (element.type !== "video" || !(element as any).isReversed) {
      return;
    }

    const media = document.getElementById(
      element.properties.elementId
    ) as HTMLVideoElement;
    if (!media) return;

    // 预加载视频的元数据
    if (media.readyState < 1) {
      media.addEventListener(
        "loadedmetadata",
        () => {
          // 设置预加载策略
          media.preload = "metadata";
        },
        { once: true }
      );
    }
  }

  // ==================== 清理方法 ====================

  destroy() {
    // Cancel any ongoing animation frame
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = undefined;
    }

    // Stop and properly destroy the animation timeline
    if (this.animationTimeLine) {
      this.animationTimeLine.pause();
      anime.remove(this.animationTimeLine);
      this.animationTimeLine = null as any;
    }

    // Clear wheel animation frame
    if (this._wheelAnimationFrame) {
      cancelAnimationFrame(this._wheelAnimationFrame);
      this._wheelAnimationFrame = null;
    }

    // Reset properties
    this.playing = false;
    this.currentKeyFrame = 0;
    this.startedTime = 0;
    this.startedTimePlay = 0;
    this.reverseVideoCache.clear();
  }
}
