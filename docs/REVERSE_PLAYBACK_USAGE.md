# 视频倒放功能使用指南

## 🎯 功能简介

视频倒放功能允许您将视频或音频元素设置为倒放播放，创造有趣的视觉效果。该功能在前端预览和最终导出的视频中都能正常工作。

## 📋 使用步骤

### 1. 添加媒体元素
- 从左侧菜单添加视频或音频元素到时间轴
- 确保元素已正确加载并显示在画布上

### 2. 选择元素
- 点击时间轴上的视频或音频元素
- 或者点击画布上的视频元素
- 确保元素被选中（高亮显示）

### 3. 开启倒放
- 在右侧属性面板中找到对应的控制面板：
  - 视频元素：在"Video"标签页中
  - 音频元素：在"Audio"面板中
- 找到"倒放"控制区域
- 切换倒放开关到"开启"状态

### 4. 预览效果
- 点击时间轴的播放按钮
- 观察视频/音频的倒放效果
- 可以拖拽时间轴指示器来查看不同时间点的效果

### 5. 导出视频
- 设置完成后，点击导出按钮
- 生成的视频将包含正确的倒放效果

## 🔧 功能特性

### ✅ 支持的元素类型
- **视频元素**：完整的视频倒放支持
- **音频元素**：音频倒放支持

### ✅ 兼容性
- **播放速度**：可以与变速功能组合使用
- **滤镜效果**：与亮度、对比度等滤镜兼容
- **其他功能**：与裁剪、旋转等功能兼容

### ✅ 状态管理
- **自动保存**：倒放状态会自动保存到项目中
- **撤销重做**：支持撤销和重做操作
- **持久化**：页面刷新后状态保持不变

## 💡 使用技巧

### 创意应用
1. **回忆效果**：将视频倒放创造回忆或梦境效果
2. **魔术效果**：倒放破碎或消失的过程
3. **运动效果**：倒放跳跃、投掷等动作
4. **音效创意**：倒放音频创造特殊音效

### 最佳实践
1. **性能考虑**：倒放会增加处理时间，建议在最终确定后再开启
2. **预览确认**：开启倒放后务必预览效果确保符合预期
3. **组合使用**：可以与慢动作结合使用增强效果
4. **音视频同步**：注意音视频倒放的同步效果

## ⚠️ 注意事项

### 性能影响
- 倒放功能会增加视频处理的内存使用
- 导出时间可能会相应增加
- 建议在配置较好的设备上使用

### 技术限制
- HTML5 video元素本身不支持倒放，前端预览通过时间计算实现
- 某些浏览器可能在预览时有轻微的性能差异
- 最终导出效果以FFmpeg处理结果为准

## 🐛 故障排除

### 常见问题
1. **倒放开关无响应**
   - 确保元素已正确选中
   - 检查元素类型是否为视频或音频

2. **预览效果不明显**
   - 选择动作较明显的视频片段测试
   - 确保时间轴正在播放状态

3. **导出视频无倒放效果**
   - 检查倒放开关是否已开启
   - 确认导出过程没有错误

### 获取帮助
如果遇到问题，请检查：
- 浏览器控制台是否有错误信息
- 视频文件格式是否支持
- 网络连接是否稳定
