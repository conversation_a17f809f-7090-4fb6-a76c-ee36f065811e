# 视频倒放功能实现文档

## 功能概述

本文档描述了在Fabric视频编辑器中实现的视频/音频倒放功能。该功能允许用户将视频或音频元素设置为倒放播放，在前端预览和后端视频生成中都能正确工作。

## 功能特性

### 前端功能
- ✅ 支持视频元素倒放
- ✅ 支持音频元素倒放
- ✅ 实时预览倒放效果
- ✅ 倒放状态持久化保存
- ✅ 撤销/重做支持

### 后端功能
- ✅ FFmpeg视频倒放处理（使用reverse滤镜）
- ✅ FFmpeg音频倒放处理（使用areverse滤镜）
- ✅ 与播放速度功能兼容
- ✅ 与其他滤镜效果兼容

## 使用方法

### 前端操作
1. 选择视频或音频元素
2. 在右侧属性面板中找到"倒放"控制
3. 切换倒放开关
4. 在时间轴播放时可以看到倒放效果

### 技术实现

#### 前端实现
1. **类型定义**：在`EditorElementBase`中添加`isReversed`属性
2. **状态管理**：在Store中添加`setElementReversed`方法
3. **播放逻辑**：在TimelineManager中处理倒放的时间计算
4. **UI组件**：创建ReverseControl组件提供用户界面

#### 后端实现
1. **类型定义**：在MediaElement接口中添加`isReversed`属性
2. **视频处理**：在VideoFilterGenerator中使用FFmpeg的`reverse`滤镜
3. **音频处理**：在AudioFilterGenerator中使用FFmpeg的`areverse`滤镜

## 技术细节

### 前端倒放逻辑
```typescript
if (isReversed) {
  // 倒放逻辑：从元素结束时间开始倒推
  const elementDuration = (element.timeFrame.end - element.timeFrame.start) / 1000;
  const progressInElement = (newTime - element.timeFrame.start) / 1000;
  const reversedProgress = elementDuration - progressInElement;
  mediaTime = reversedProgress * originalPlaybackSpeed + mediaStartTime;
} else {
  // 正常播放逻辑
  mediaTime = ((newTime - element.timeFrame.start) / 1000) * originalPlaybackSpeed + mediaStartTime;
}
```

### 后端FFmpeg滤镜
- **视频倒放**：使用`reverse`滤镜
- **音频倒放**：使用`areverse`滤镜

## 注意事项

1. **性能影响**：倒放功能会增加视频处理的内存使用和处理时间
2. **兼容性**：与播放速度、滤镜等其他功能完全兼容
3. **限制**：HTML5 video元素本身不支持倒放，前端预览通过时间计算实现

## 测试建议

1. 测试视频元素倒放
2. 测试音频元素倒放
3. 测试倒放与变速的组合
4. 测试倒放与滤镜的组合
5. 测试导出视频的倒放效果

## 未来改进

1. 添加倒放预览优化
2. 支持部分倒放（指定倒放区间）
3. 添加倒放动画效果
4. 优化倒放性能
