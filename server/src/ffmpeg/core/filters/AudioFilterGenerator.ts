import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";

/**
 * Filter generator for audio elements
 * Handles audio-specific operations like volume, playback speed, and fades
 */
export class AudioFilterGenerator extends AbstractFilterGenerator {
  /**
   * Generates an FFmpeg filter string for audio elements
   *
   * @param inputIndex Index of the input stream or -1 for original
   * @param element Canvas element to generate filter for
   * @param startTime Start time in milliseconds
   * @param duration Duration in seconds
   * @param index Index of the filter
   * @param totalDuration Optional total duration of the output
   * @param streamIndex Optional stream index for output labeling
   * @returns FFmpeg filter string for the audio element
   */
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number,
    totalDuration?: number,
    streamIndex?: number
  ): string {
    let filterString = `[${inputIndex}:a]`;

    // 获取mediaStartTime属性，如果不存在则默认为0
    const mediaStartTime = element.properties.mediaStartTime
      ? Number(element.properties.mediaStartTime)
      : 0;
    console.log("audio media start time", mediaStartTime);

    // Calculate source duration considering playback speed
    // 这应该与VideoFilterGenerator中的逻辑保持一致
    let sourceDuration = duration;
    if (element.playbackSpeed !== undefined && element.playbackSpeed > 0) {
      // 播放速度越快，需要更多的源音频内容
      // 播放速度越慢，需要更少的源音频内容
      // 例如：显示6.83秒，1.9倍速，需要6.83 * 1.9 = 12.98秒的源音频
      sourceDuration = duration * element.playbackSpeed;

      console.log(
        `变速音频源时长计算: 显示${duration}s × ${element.playbackSpeed}x = ${sourceDuration}s`
      );
    }

    // 从mediaStartTime开始裁剪音频，持续sourceDuration秒
    filterString +=
      `atrim=start=${mediaStartTime.toFixed(
        3
      )}:duration=${sourceDuration.toFixed(3)},` + `asetpts=PTS-STARTPTS`;

    // Add volume control if specified
    if (element.volume !== undefined) {
      filterString += `,volume=${element.volume.toFixed(2)}`;
    }

    // Add playback speed control if specified
    if (element.playbackSpeed !== undefined) {
      filterString += this.generatePlaybackSpeedFilter(element.playbackSpeed);
    }

    // Add reverse playback if specified
    if (element.isReversed) {
      filterString += ",areverse";
    }

    // Add fade effects
    // Calculate reasonable fade durations: max 1 second, min 10% of total duration or 0.2s
    const fadeInDuration = Math.min(1, Math.max(sourceDuration * 0.1, 0.2));
    const fadeOutDuration = Math.min(1, Math.max(sourceDuration * 0.1, 0.2));

    // Fade in from start time
    filterString += `,afade=t=in:st=0:d=${fadeInDuration.toFixed(3)}`;

    // Fade out before end
    filterString += `,afade=t=out:st=${(
      sourceDuration - fadeOutDuration
    ).toFixed(3)}:d=${fadeOutDuration.toFixed(3)}`;

    // If total duration provided, pad audio to match
    if (totalDuration) {
      filterString += `,apad=whole_dur=${duration.toFixed(3)}`;
    } else {
      // 如果没有提供总时长，使用元素自身的显示时长（而不是源时长）
      filterString += `,apad=whole_dur=${duration.toFixed(3)}`;
    }

    // 设置音频的时间戳，确保与视频画面同步
    // 无论startTime是否大于0，我们都需要确保音频与视频画面同步
    // 使用与视频部分完全相同的时间戳格式和精度
    // 视频部分使用 toFixed(2) 保留两位小数，这里也应该保持一致
    const timeFrameStart = Math.floor(element.timeFrame.start) / 1000;

    // 对于所有视频，我们都使用adelay来延迟音频的播放时间
    // 这样可以确保所有视频的声音都与视频画面完全同步
    // 使用adelay可以在保持音频内容不变的情况下延迟音频的播放时间
    const delayMs = Math.round(timeFrameStart * 1000);
    if (delayMs > 0) {
      filterString += `,adelay=${delayMs}|${delayMs}:all=1`;
    }

    // 注意：aresample 和 dynaudnorm 现在在 CommandBuilder.addAudioMix 中添加
    // 这样可以确保在混音后应用这些处理

    // 总是添加输出标签，如果streamIndex未定义，使用index替代
    const outputIndex = streamIndex !== undefined ? streamIndex : index;
    filterString += `[a${outputIndex}]`;

    return filterString;
  }

  /**
   * Generates filter chain for playback speed adjustment
   * Handles speed values outside FFmpeg's atempo filter range (0.5-2.0)
   * by chaining multiple filters
   *
   * @param playbackSpeed Speed multiplier
   * @returns Filter string for playback speed adjustment
   */
  private generatePlaybackSpeedFilter(playbackSpeed: number): string {
    // FFmpeg's atempo filter is limited to 0.5-2.0 range
    // For values outside this range, we need to chain multiple atempo filters
    let filterPart = "";
    let remainingSpeed = playbackSpeed;

    // Handle extreme values
    if (remainingSpeed < 0.125) {
      remainingSpeed = 0.125; // Minimum supported speed (0.5^3)
    }

    if (remainingSpeed > 16.0) {
      remainingSpeed = 16.0; // Maximum supported speed (2.0^4)
    }

    // Handle speeds < 0.5 by chaining 0.5 filters
    while (remainingSpeed < 0.5) {
      filterPart += `,atempo=0.5`;
      remainingSpeed = remainingSpeed / 0.5; // Adjust remaining speed
    }

    // Handle speeds > 2.0 by chaining 2.0 filters
    while (remainingSpeed > 2.0) {
      filterPart += `,atempo=2.0`;
      remainingSpeed = remainingSpeed / 2.0; // Adjust remaining speed
    }

    // Handle remaining speed between 0.5-2.0
    if (remainingSpeed != 1.0) {
      filterPart += `,atempo=${remainingSpeed.toFixed(2)}`;
    }

    return filterPart;
  }
}
