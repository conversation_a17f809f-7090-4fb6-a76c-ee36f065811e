import { CanvasState } from "../../types";

export interface BaseFilterGenerator {
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number
  ): string;
}

export abstract class AbstractFilterGenerator implements BaseFilterGenerator {
  /**
   * Applies visual effects to the filter chain
   * @param filterString Current filter string
   * @param effect Effect configuration object
   * @returns Updated filter string with effect applied
   */
  protected applyEffects(
    filterString: string,
    effect?: { type: string }
  ): string {
    if (!effect?.type) return filterString;

    // Map of predefined visual effects with their corresponding FFmpeg filter strings
    const effectMap: Record<string, string> = {
      blackAndWhite: ",hue=s=0", // Remove all saturation
      sepia:
        ",colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131", // Apply sepia tone matrix
      invert: ",negate", // Invert all colors
      saturate: ",eq=saturation=2", // Double the saturation
    };

    return filterString + (effectMap[effect.type] || "");
  }

  /**
   * Applies adjustable filters like brightness, contrast, etc.
   * @param filterString Current filter string
   * @param filters Object containing filter values
   * @returns Updated filter string with adjustments applied
   */
  protected applyFilters(filterString: string, filters?: any): string {
    if (!filters) return filterString;

    // Define filter configurations for cleaner processing
    // Each config specifies:
    // - property: the filter property name in the input object
    // - condition: when to apply the filter (usually non-zero/default values)
    // - transform: how to convert the input value to FFmpeg filter syntax
    const filterConfigs = [
      {
        property: "brightness",
        condition: (val: number) => val !== 0,
        transform: (val: number) => `,eq=brightness=${val / 100}`, // Convert to -1.0 to 1.0 range
      },
      {
        property: "contrast",
        condition: (val: number) => val !== 0,
        transform: (val: number) => `,eq=contrast=${1 + val / 100}`, // Convert to 0.0 to 2.0 range
      },
      {
        property: "saturation",
        condition: (val: number) => val !== 0,
        transform: (val: number) => `,eq=saturation=${1 + val / 100}`, // Convert to 0.0 to 2.0 range
      },
      {
        property: "hue",
        condition: (val: number) => val !== 0,
        transform: (val: number) => `,hue=h=${val}`, // Direct hue angle value
      },
      {
        property: "blur",
        condition: (val: number) => val > 0,
        transform: (val: number) => `,boxblur=${val / 10}`, // Scale down for more reasonable blur values
      },
      {
        property: "noise",
        condition: (val: number) => val > 0,
        transform: (val: number) => `,noise=alls=${val / 100}:allf=t`, // Convert to 0.0 to 1.0 range
      },
    ];

    // Apply each filter if it meets the condition
    for (const config of filterConfigs) {
      const value = filters[config.property];
      if (value !== undefined && !isNaN(value) && config.condition(value)) {
        filterString += config.transform(value);
      }
    }

    return filterString;
  }

  /**
   * Adds a border around the element
   * @param filterString Current filter string
   * @param border Border configuration with width, color, style, and borderRadius
   * @returns Updated filter string with border applied
   */
  protected applyBorder(
    filterString: string,
    border?: {
      width: number;
      color: string;
      style?: string;
      borderRadius?: number;
    }
  ): string {
    if (border && border.width > 0) {
      const width = border.width;
      const color = border.color;
      const style = border.style || "solid";
      const borderRadius = border.borderRadius || 0;

      // Apply border radius if specified
      if (borderRadius > 0) {
        // Implement rounded corners using geq filter with alpha masking
        // This creates a rounded rectangle mask and applies it to the bordered image
        return this.applyRoundedBorder(
          filterString,
          width,
          color,
          borderRadius
        );
      }

      // Apply border based on style
      if (style === "dashed" || style === "dotted") {
        // For dashed/dotted borders, we need to create a more complex filter
        // For now, fall back to solid border with a note
        console.log(
          `Note: ${style} border style not fully implemented, using solid border`
        );
      }

      // Pad the image with specified color to create border effect
      // This adds equal padding on all sides with the border color
      filterString += `,pad=w=iw+${width * 2}:h=ih+${
        width * 2
      }:x=${width}:y=${width}:color=${color}`;
    }
    return filterString;
  }

  /**
   * Applies rounded border using geq filter with alpha masking
   * @param filterString Current filter string
   * @param width Border width
   * @param color Border color
   * @param borderRadius Corner radius
   * @returns Updated filter string with rounded border applied
   */
  protected applyRoundedBorder(
    filterString: string,
    width: number,
    color: string,
    borderRadius: number
  ): string {
    // First, pad the image with border color
    const paddedFilter = `,pad=w=iw+${width * 2}:h=ih+${
      width * 2
    }:x=${width}:y=${width}:color=${color}`;

    // Create rounded rectangle mask using geq filter with simplified logic
    // Use multiplication instead of 'and' function for better compatibility
    const maskFilter =
      `,split[main][mask];[mask]geq=` +
      `lum='255':` +
      `a='st(0,${borderRadius});` + // Store border radius in variable 0
      `st(1,W-ld(0));` + // Store right edge (W - radius) in variable 1
      `st(2,H-ld(0));` + // Store bottom edge (H - radius) in variable 2
      `if(` +
      // Top-left corner: X < radius AND Y < radius
      `lt(X,ld(0))*lt(Y,ld(0)),` + // Use multiplication instead of 'and'
      `if(lte(hypot(X-ld(0),Y-ld(0)),ld(0)),255,0),` +
      `if(` +
      // Top-right corner: X > (W-radius) AND Y < radius
      `gt(X,ld(1))*lt(Y,ld(0)),` +
      `if(lte(hypot(X-ld(1),Y-ld(0)),ld(0)),255,0),` +
      `if(` +
      // Bottom-right corner: X > (W-radius) AND Y > (H-radius)
      `gt(X,ld(1))*gt(Y,ld(2)),` +
      `if(lte(hypot(X-ld(1),Y-ld(2)),ld(0)),255,0),` +
      `if(` +
      // Bottom-left corner: X < radius AND Y > (H-radius)
      `lt(X,ld(0))*gt(Y,ld(2)),` +
      `if(lte(hypot(X-ld(0),Y-ld(2)),ld(0)),255,0),` +
      `255` + // All other pixels (non-corner areas) are opaque
      `)` +
      `)` +
      `)` +
      `)'` +
      `[roundmask];[main][roundmask]alphamerge`;

    return filterString + paddedFilter + maskFilter;
  }

  /**
   * Creates the initial filter string with the appropriate input label
   * @param inputIndex Input stream index or -1 for original
   * @param index Element index for labeling
   * @returns Initial filter string with input label
   */
  protected getInitialFilterString(inputIndex: number, index: number): string {
    // If inputIndex is -1, use the original source with element index
    // Otherwise use the specified input stream
    return inputIndex === -1 ? `[original${index}]` : `[${inputIndex}:v]`;
  }

  /**
   * Scales and crops the element to fit target dimensions while maintaining aspect ratio
   * @param filterString Current filter string
   * @param targetWidth Target width in pixels
   * @param targetHeight Target height in pixels
   * @param cropParams Optional custom crop parameters
   * @returns Updated filter string with scaling and cropping applied
   */
  protected applyScaleAndCrop(
    filterString: string,
    targetWidth: number,
    targetHeight: number,
    cropParams?: {
      cropX: number;
      cropY: number;
      cropWidth: number;
      cropHeight: number;
    }
  ): string {
    if (cropParams) {
      // 如果有自定义剪裁参数，直接应用剪裁
      const { cropX, cropY, cropWidth, cropHeight } = cropParams;

      // 首先从原始图片中剪裁出指定区域
      filterString += `crop=${Math.round(cropWidth)}:${Math.round(
        cropHeight
      )}:${Math.round(cropX)}:${Math.round(cropY)}`;

      // 然后缩放到目标尺寸
      filterString += `,scale=${targetWidth}:${targetHeight}`;

      // 设置像素宽高比为1:1
      filterString += `,setsar=1`;
    } else {
      // 原有的逻辑：先缩放后居中剪裁
      // Scale to fit within target dimensions while preserving aspect ratio
      // The conditional expressions ensure the image fits within the target dimensions
      // without distortion by scaling the appropriate dimension
      filterString +=
        `scale='if(gte(iw/ih,${targetWidth}/${targetHeight}),` +
        `-1,${targetWidth}):if(gte(iw/ih,${targetWidth}/${targetHeight}),` +
        `${targetHeight},-1)',setsar=1`;

      // Center crop to exact target dimensions
      // This ensures the final output is exactly the requested size
      filterString += `,crop=${targetWidth}:${targetHeight}:(iw-${targetWidth})/2:(ih-${targetHeight})/2`;
    }

    return filterString;
  }

  /**
   * Applies rotation to the element
   * @param filterString Current filter string
   * @param rotation Rotation angle in degrees
   * @param targetWidth Target width in pixels
   * @param targetHeight Target height in pixels
   * @returns Updated filter string with rotation applied
   */
  protected applyRotation(
    filterString: string,
    rotation: number,
    targetWidth: number,
    targetHeight: number
  ): string {
    if (!rotation) return filterString;

    // Convert rotation to radians and calculate trigonometric values
    const rotationRad = (rotation * Math.PI) / 180;
    const cosTheta = Math.abs(Math.cos(rotationRad));
    const sinTheta = Math.abs(Math.sin(rotationRad));

    // Calculate maximum dimension needed to contain rotated image
    // This ensures no part of the image is clipped during rotation
    const maxDimension = Math.ceil(
      Math.sqrt(Math.pow(targetWidth, 2) + Math.pow(targetHeight, 2))
    );

    // Pad the image to prevent clipping during rotation
    // Uses transparent padding (black@0) to maintain alpha channel
    filterString +=
      `,pad=${maxDimension}:${maxDimension}:` +
      `(${maxDimension}-iw)/2:(${maxDimension}-ih)/2:color=black@0`;

    // Apply rotation with transparent background
    // c=none ensures transparency is preserved
    filterString += `,rotate=${rotation}*PI/180:c=none`;

    // Calculate dimensions after rotation using trigonometric formulas
    const rotatedWidth = Math.round(
      targetWidth * cosTheta + targetHeight * sinTheta
    );
    const rotatedHeight = Math.round(
      targetWidth * sinTheta + targetHeight * cosTheta
    );

    // Crop to the rotated dimensions to remove excess transparent padding
    filterString +=
      `,crop=${rotatedWidth}:${rotatedHeight}:` +
      `(iw-${rotatedWidth})/2:(ih-${rotatedHeight})/2`;

    return filterString;
  }

  /**
   * Ensures the output format includes alpha channel
   * @param filterString Current filter string
   * @returns Updated filter string with RGBA format applied
   */
  protected applyFormat(filterString: string): string {
    // Force RGBA format to ensure alpha channel is preserved throughout the filter chain
    return filterString + ",format=rgba";
  }

  /**
   * Applies opacity/transparency to the element
   * @param filterString Current filter string
   * @param opacity Opacity value (0-1)
   * @returns Updated filter string with opacity applied
   */
  protected applyOpacity(filterString: string, opacity?: number): string {
    if (opacity !== undefined && opacity !== 1) {
      // Modify only the alpha channel using colorchannelmixer
      // This preserves the RGB values while adjusting transparency
      filterString += `,colorchannelmixer=aa=${opacity}`;
    }
    return filterString;
  }

  /**
   * Applies horizontal and/or vertical flipping
   * @param filterString Current filter string
   * @param flipX Whether to flip horizontally
   * @param flipY Whether to flip vertically
   * @returns Updated filter string with flips applied
   */
  protected applyFlips(
    filterString: string,
    flipX?: boolean,
    flipY?: boolean
  ): string {
    if (flipX) filterString += ",hflip"; // Horizontal flip (mirror effect)
    if (flipY) filterString += ",vflip"; // Vertical flip (upside down)
    return filterString;
  }

  /**
   * Applies reverse playback to the video
   * @param filterString Current filter string
   * @returns Updated filter string with reverse playback applied
   */
  protected applyReverse(filterString: string): string {
    // Use the reverse filter to play video backwards
    // This filter reads the entire video into memory and plays it in reverse
    filterString += ",reverse";
    return filterString;
  }

  /**
   * Abstract method to be implemented by subclasses
   * Generates the complete filter string for an element
   */
  abstract generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number
  ): string;
}
