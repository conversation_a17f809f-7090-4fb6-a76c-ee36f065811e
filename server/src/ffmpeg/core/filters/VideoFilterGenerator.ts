import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";

export class VideoFilterGenerator extends AbstractFilterGenerator {
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    duration: number,
    index: number
  ): string {
    const { placement, properties, timeFrame } = element;
    const { filters, effect } = properties;

    // 获取mediaStartTime属性，如果不存在则默认为0
    const mediaStartTime = properties.mediaStartTime
      ? Number(properties.mediaStartTime)
      : 0;

    if (!placement) {
      throw new Error(`Video element at index ${index} missing placement data`);
    }
    console.log("video duration", duration);
    console.log("media start time", mediaStartTime);

    // 计算需要从源视频中提取的实际时长
    // 对于变速视频，我们需要足够的源内容来填满显示时长
    let sourceDuration = duration;
    if (element.playbackSpeed !== undefined && element.playbackSpeed > 0) {
      // 播放速度越快，需要更多的源视频内容
      // 播放速度越慢，需要更少的源视频内容
      // 例如：显示6.83秒，1.9倍速，需要6.83 * 1.9 = 12.98秒的源内容
      sourceDuration = duration * element.playbackSpeed;

      console.log(
        `变速视频源时长计算: 显示${duration}s × ${element.playbackSpeed}x = ${sourceDuration}s`
      );
    }

    // 只进行基本的视频裁剪和播放速度调整，不处理时间戳偏移
    // 时间戳偏移将在MediaOverlayFilterGenerator中统一处理
    let filterString =
      this.getInitialFilterString(inputIndex, index) +
      // 从mediaStartTime开始裁剪视频，持续sourceDuration秒
      `trim=start=${mediaStartTime.toFixed(
        3
      )}:duration=${sourceDuration.toFixed(3)},` +
      // 重置时间戳到0，不添加时间偏移
      `setpts=PTS-STARTPTS,`;

    // Apply playback speed if specified
    if (element.playbackSpeed !== undefined) {
      filterString = this.applyPlaybackSpeed(
        filterString,
        element.playbackSpeed
      );
    }

    // Apply reverse playback if specified
    if (element.isReversed) {
      filterString = this.applyReverse(filterString);
    }

    // Scale and crop
    const targetWidth = Math.round(placement.width);
    const targetHeight = Math.round(placement.height);

    // 检查是否有自定义剪裁参数
    const cropParams =
      placement.cropX !== undefined &&
      placement.cropY !== undefined &&
      placement.cropWidth !== undefined &&
      placement.cropHeight !== undefined
        ? {
            cropX: placement.cropX,
            cropY: placement.cropY,
            cropWidth: placement.cropWidth,
            cropHeight: placement.cropHeight,
          }
        : undefined;

    filterString = this.applyScaleAndCrop(
      filterString,
      targetWidth,
      targetHeight,
      cropParams
    );
    // Handle rotation
    filterString = this.applyRotation(
      filterString,
      placement.rotation || 0,
      targetWidth,
      targetHeight
    );

    // Set format
    filterString = this.applyFormat(filterString);

    // Apply flips
    filterString = this.applyFlips(
      filterString,
      placement.flipX,
      placement.flipY
    );

    // Apply opacity
    filterString = this.applyOpacity(filterString, element.opacity);

    // Apply volume if specified
    if (element.volume !== undefined) {
      filterString = this.applyVolume(filterString, element.volume);
    }

    // Apply common filters
    filterString = this.applyEffects(filterString, effect as any);
    filterString = this.applyFilters(filterString, filters);
    filterString = this.applyBorder(filterString, properties.border as any);

    return filterString + `[vid${index}]`;
  }

  /**
   * Applies playback speed adjustment to video
   * @param filterString Current filter string
   * @param playbackSpeed Speed multiplier (0.5-2.0 recommended)
   * @returns Updated filter string with speed adjustment applied
   */
  private applyPlaybackSpeed(
    filterString: string,
    playbackSpeed: number
  ): string {
    if (playbackSpeed === 1.0) return filterString;

    // For video, we need to adjust the PTS to change playback speed
    const ptsMultiplier = 1.0 / playbackSpeed;

    // Replace the setpts filter to adjust the playback speed
    const setptsRegex = /setpts=PTS-STARTPTS/;
    const match = filterString.match(setptsRegex);

    if (match) {
      // Replace with speed-adjusted setpts
      filterString = filterString.replace(
        setptsRegex,
        `setpts=${ptsMultiplier}*(PTS-STARTPTS)`
      );
    } else {
      // If no existing setpts found (shouldn't happen), append the speed filter
      filterString = filterString + `,setpts=${ptsMultiplier}*PTS`;
    }

    console.log(
      `应用播放速度调整: ${playbackSpeed}x, PTS倍数: ${ptsMultiplier}`
    );

    return filterString;
  }

  /**
   * Applies volume adjustment to video's audio track
   * @param filterString Current filter string
   * @param volume Volume level (0.0-1.0 recommended)
   * @returns Updated filter string with volume adjustment applied
   */
  private applyVolume(filterString: string, volume: number): string {
    // Note: This doesn't directly affect the video filter chain
    // The actual volume adjustment happens in the audio processing
    // This is a placeholder in case we need to mark videos that need volume adjustment
    return filterString;
  }
}
