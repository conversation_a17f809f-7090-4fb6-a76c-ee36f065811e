import { Transition } from "../types";

/**
 * Represents the state of the canvas with all media elements and configuration
 */
export interface CanvasState {
  /** Canvas width in pixels */
  width: number;
  /** Canvas height in pixels */
  height: number;
  /** Background color in hex or rgba format */
  backgroundColor: string;
  /** Array of media elements to be rendered */
  elements: MediaElement[];
  /** Output format configuration */
  outputFormat?: OutputFormat;
  /** Optional captions/subtitles */
  captions?: Caption[];
  /** Global caption style configuration */
  globalCaptionStyle?: CaptionStyle;
}

/**
 * Defines the position and transformation of a media element on the canvas
 */
export interface Placement {
  /** X coordinate (left) in pixels */
  x: number;
  /** Y coordinate (top) in pixels */
  y: number;
  /** Width in pixels */
  width: number;
  /** Height in pixels */
  height: number;
  /** Rotation in degrees (clockwise) */
  rotation?: number;
  /** Horizontal scale factor */
  scaleX: number;
  /** Vertical scale factor */
  scaleY: number;
  /** Whether the element is flipped horizontally */
  flipX?: boolean;
  /** Whether the element is flipped vertically */
  flipY?: boolean;
  /** Crop X coordinate in original image pixels */
  cropX?: number;
  /** Crop Y coordinate in original image pixels */
  cropY?: number;
  /** Crop width in original image pixels */
  cropWidth?: number;
  /** Crop height in original image pixels */
  cropHeight?: number;
}

/**
 * Defines the time range when a media element appears in the video
 */
export interface TimeFrame {
  /** Start time in milliseconds */
  start: number;
  /** End time in milliseconds */
  end: number;
}

/**
 * Represents a visual or audio effect to be applied to a media element
 */
export interface Effect {
  /** Type of effect (e.g., 'blur', 'sepia', etc.) */
  type: string;
  /** Effect-specific parameters */
  params: Record<string, unknown>;
}

/**
 * Defines a border to be applied around a media element
 */
export interface Border {
  /** Border color in hex or rgba format */
  color: string;
  /** Border width in pixels */
  width: number;
  /** Border style (e.g., 'solid', 'dashed', etc.) */
  style: string;
}

// Transition interface moved to server/src/types.ts to avoid duplication

/**
 * Represents a media element (video, image, audio, text, shape, or gif) on the canvas
 */
export interface MediaElement {
  /** Type of media element */
  type: "video" | "image" | "audio" | "text" | "shape" | "gif";
  /** Unique identifier */
  id: string;
  /** Type-specific properties */
  properties: Record<string, unknown>;
  /** Opacity (0-1) */
  opacity?: number;
  /** Position and transformation */
  placement?: Placement;
  /** Time range when the element appears */
  timeFrame: TimeFrame;
  /** Visual or audio effects */
  effects?: Effect[];
  /** Border configuration */
  border?: Border;
  /** Transition effects */
  transition?: Transition;
  /** Audio volume (0-1) */
  volume?: number;
  /** Playback speed multiplier */
  playbackSpeed?: number;
  /** Whether to play in reverse */
  isReversed?: boolean;
  /** Duration in seconds (may differ from timeFrame due to playbackSpeed) */
  duration?: number;
}

/**
 * Defines the output format configuration for the generated video
 */
export interface OutputFormat {
  /** Video codec (e.g., 'libx264', 'h264_nvenc', etc.) */
  codec: string;
  /** Output container format (e.g., 'mp4', 'webm', etc.) */
  format: string;
  /** Quality preset */
  quality: "low" | "medium" | "high";
  /** Frame rate in frames per second */
  frameRate: number;
}

/**
 * Encoding parameters for the video codec
 */
export interface EncodingParams {
  /** Encoding speed preset (e.g., 'ultrafast', 'medium', 'slow') */
  preset: string;
  /** Constant Rate Factor (0-51, lower means better quality) */
  crf: number;
  /** Encoding profile (e.g., 'baseline', 'main', 'high') */
  profile: string;
}

/**
 * Represents a caption/subtitle in the video
 */
export interface Caption {
  /** Unique identifier */
  id: string;
  /** Start time in format "00:00:00" */
  startTime: string;
  /** End time in format "00:00:00" */
  endTime: string;
  /** Caption text content */
  text: string;
}

/**
 * Global caption style configuration
 */
export interface CaptionStyle {
  /** Font size in pixels */
  fontSize: number;
  /** Font family name */
  fontFamily: string;
  /** Font color in hex format */
  fontColor: string;
  /** Font weight (400 = normal, 700 = bold) */
  fontWeight: number;
  /** Text alignment */
  textAlign: "left" | "center" | "right";
  /** Line height multiplier */
  lineHeight: number;
  /** Character spacing in pixels */
  charSpacing: number;
  /** Text styles array (bold, italic, underline) */
  styles: string[];
  /** Stroke/outline width in pixels */
  strokeWidth: number;
  /** Stroke/outline color in hex format */
  strokeColor: string;
  /** Shadow color in hex format */
  shadowColor: string;
  /** Shadow blur radius in pixels */
  shadowBlur: number;
  /** Shadow offset X in pixels */
  shadowOffsetX: number;
  /** Shadow offset Y in pixels */
  shadowOffsetY: number;
  /** Background color in hex format */
  backgroundColor: string;
  /** Whether to use gradient */
  useGradient: boolean;
  /** Gradient colors array */
  gradientColors: string[];
  /** Caption position X offset (relative to center) */
  positionX?: number;
  /** Caption position Y offset (relative to default bottom position) */
  positionY?: number;
  /** Horizontal origin point */
  originX?: "left" | "center" | "right";
  /** Vertical origin point */
  originY?: "top" | "center" | "bottom";
  /** Text box width (if provided by frontend) */
  width?: number;
  /** Text box height (if provided by frontend) */
  height?: number;
  /** Text opacity (0-1) */
  opacity?: number;
  /** Scale factor X (for handling canvas control box scaling) */
  scaleX?: number;
  /** Scale factor Y (for handling canvas control box scaling) */
  scaleY?: number;
}
